<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - VAITH Admin</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Enhanced Sidebar with Accessibility -->
        <aside class="admin-sidebar" id="adminSidebar" role="navigation" aria-label="Admin navigation" aria-hidden="false">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo" aria-label="VAITH Admin Dashboard">VAITH</a>
            </div>
            <nav class="sidebar-nav" role="menu">
                <div class="nav-item" role="none">
                    <a href="admin-dashboard.html" class="nav-link" role="menuitem">
                        <i class="nav-icon fas fa-chart-line" aria-hidden="true"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item" role="none">
                    <a href="admin-users.html" class="nav-link active" role="menuitem" aria-current="page">
                        <i class="nav-icon fas fa-users" aria-hidden="true"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item" role="none">
                    <a href="admin-products.html" class="nav-link" role="menuitem">
                        <i class="nav-icon fas fa-box" aria-hidden="true"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item" role="none">
                    <a href="admin-orders.html" class="nav-link" role="menuitem">
                        <i class="nav-icon fas fa-shopping-cart" aria-hidden="true"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
                <div class="nav-item" role="none">
                    <a href="admin-analytics.html" class="nav-link" role="menuitem">
                        <i class="nav-icon fas fa-chart-bar" aria-hidden="true"></i>
                        <span class="nav-text">Analytics</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Mobile Sidebar Overlay -->
        <div class="admin-sidebar-overlay" id="sidebarOverlay" aria-hidden="true"></div>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Enhanced Header with Accessibility -->
            <header class="admin-header" role="banner">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle"
                            aria-label="Toggle navigation sidebar"
                            aria-expanded="false"
                            aria-controls="adminSidebar">
                        <i class="fas fa-bars" aria-hidden="true"></i>
                    </button>
                    <h1 class="page-title">User Management</h1>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle"
                            title="Toggle dark mode"
                            aria-label="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon" aria-hidden="true"></i>
                    </button>
                    <div class="user-menu">
                        <button class="nav-icon" id="userMenuBtn"
                                aria-label="User menu"
                                aria-expanded="false"
                                aria-haspopup="true"
                                aria-controls="userDropdown">
                            <i class="fas fa-user-circle" aria-hidden="true"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdown"
                             role="menu"
                             aria-hidden="true"
                             aria-labelledby="userMenuBtn">
                            <a href="user-profile.html" class="dropdown-item" role="menuitem">
                                <i class="fas fa-user" aria-hidden="true"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item" role="menuitem">
                                <i class="fas fa-cog" aria-hidden="true"></i> Settings
                            </a>
                            <hr class="dropdown-divider" role="separator">
                            <a href="#" class="dropdown-item" id="logoutBtn" role="menuitem">
                                <i class="fas fa-sign-out-alt" aria-hidden="true"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb Navigation -->
                <nav class="breadcrumb fade-in">
                    <div class="breadcrumb-item">
                        <a href="admin-dashboard.html" class="breadcrumb-link">
                            <i class="fas fa-home"></i> Admin
                        </a>
                    </div>
                    <div class="breadcrumb-item">
                        <span class="breadcrumb-current">User Management</span>
                    </div>
                </nav>

                <!-- Page Header -->
                <div class="page-header fade-in">
                    <h2 class="page-title">👥 User Management</h2>
                    <p class="page-subtitle">Manage user accounts, permissions, and monitor user activity across your platform.</p>
                </div>

                <!-- User Stats -->
                <div class="dashboard-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); margin-bottom: 2rem;">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalUsersCount">0</h3>
                                <p>Total Users</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="activeUsersCount">0</h3>
                                <p>Active Users</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="newUsersCount">0</h3>
                                <p>New This Month</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-user-slash"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="suspendedUsersCount">0</h3>
                                <p>Suspended</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">All Users</h3>
                        <div class="table-actions">
                            <div class="search-box" style="margin-right: 1rem;">
                                <input type="text" id="userSearch" placeholder="Search users..." style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 0.25rem; background: var(--input-bg); color: var(--text-color);">
                            </div>
                            <select id="statusFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="suspended">Suspended</option>
                            </select>
                            <select id="roleFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Roles</option>
                                <option value="user">User</option>
                                <option value="admin">Admin</option>
                            </select>
                            <button class="btn btn-primary btn-sm" id="addUserBtn" title="Add New User">
                                <i class="fas fa-plus"></i> Add User
                            </button>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th style="min-width: 200px;">User</th>
                                    <th style="min-width: 200px;">Email</th>
                                    <th style="min-width: 80px;">Role</th>
                                    <th style="min-width: 100px;">Status</th>
                                    <th style="min-width: 120px;">Join Date</th>
                                    <th style="min-width: 120px;">Last Login</th>
                                    <th style="min-width: 80px;">Orders</th>
                                    <th style="min-width: 150px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- Users will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- User Details Modal -->
    <div class="modal" id="userModal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 id="modalTitle">User Details</h3>
                <button class="close-modal" id="closeUserModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay" style="display: none;"></div>

    <!-- Modal Styles -->
    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 0.75rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            max-height: 90vh;
            overflow-y: auto;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-color);
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-light);
            cursor: pointer;
            padding: 0.25rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1999;
        }

        .user-menu {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background: var(--section-bg);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border: none;
            border-top: 1px solid var(--border-color);
        }
    </style>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/admin-common.js"></script>

    <script>
        let currentUsers = [];
        let filteredUsers = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Check admin access
            if (!authManager.requireAdmin()) {
                return;
            }

            // Initialize page
            initializeUsersPage();
            setupEventListeners();
        });

        function initializeUsersPage() {
            loadUserStats();
            loadUsers();
        }

        function loadUserStats() {
            const stats = authManager.getUserStats();
            document.getElementById('totalUsersCount').textContent = stats.total;
            document.getElementById('activeUsersCount').textContent = stats.active;
            document.getElementById('newUsersCount').textContent = stats.newThisMonth;
            document.getElementById('suspendedUsersCount').textContent = stats.suspended;
        }

        function loadUsers() {
            currentUsers = authManager.getAllUsers();
            filteredUsers = [...currentUsers];
            renderUsersTable();
        }

        function renderUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            
            if (filteredUsers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 2rem; color: var(--text-light);">No users found</td></tr>';
                return;
            }

            tbody.innerHTML = filteredUsers.map(user => `
                <tr>
                    <td>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div class="profile-avatar" style="width: 40px; height: 40px; font-size: 1rem;">
                                ${getInitials(user.firstName, user.lastName)}
                            </div>
                            <div>
                                <div style="font-weight: 500; cursor: pointer; color: var(--primary-color); text-decoration: underline;"
                                     onclick="viewUserProfile(${user.id})"
                                     title="View user profile">
                                    ${user.firstName} ${user.lastName}
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-light);">${user.phone || 'No phone'}</div>
                            </div>
                        </div>
                    </td>
                    <td>${user.email}</td>
                    <td><span class="status-badge ${user.role === 'admin' ? 'status-pending' : 'status-active'}">${user.role}</span></td>
                    <td><span class="status-badge ${getStatusBadgeClass(user.status)}">${user.status}</span></td>
                    <td>${formatDate(user.joinDate)}</td>
                    <td>${user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}</td>
                    <td>${user.stats.totalOrders}</td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-primary btn-sm" onclick="viewUserProfile(${user.id})" title="View User Profile">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="editUser(${user.id})" title="Edit User">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${user.status === 'active' ?
                                `<button class="btn btn-danger btn-sm" onclick="suspendUser(${user.id})" title="Suspend User">
                                    <i class="fas fa-ban"></i>
                                </button>` :
                                `<button class="btn btn-primary btn-sm" onclick="activateUser(${user.id})" title="Activate User">
                                    <i class="fas fa-check"></i>
                                </button>`
                            }
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function setupEventListeners() {
            // Search functionality
            document.getElementById('userSearch').addEventListener('input', function(e) {
                const query = e.target.value;
                applyFilters();
            });

            // Filter functionality
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
            document.getElementById('roleFilter').addEventListener('change', applyFilters);

            // Modal close
            document.getElementById('closeUserModal').addEventListener('click', closeModal);
            document.getElementById('overlay').addEventListener('click', closeModal);

            // Logout (sidebar/user menu handled by admin-common.js)
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                authManager.logout();
            });
        }

        function applyFilters() {
            const searchQuery = document.getElementById('userSearch').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const roleFilter = document.getElementById('roleFilter').value;

            filteredUsers = currentUsers.filter(user => {
                const matchesSearch = !searchQuery || 
                    user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    user.email.toLowerCase().includes(searchQuery.toLowerCase());

                const matchesStatus = !statusFilter || user.status === statusFilter;
                const matchesRole = !roleFilter || user.role === roleFilter;

                return matchesSearch && matchesStatus && matchesRole;
            });

            renderUsersTable();
        }

        function viewUser(userId) {
            const user = authManager.getUserById(userId);
            if (!user) return;

            document.getElementById('modalTitle').textContent = 'User Details';
            document.getElementById('userModalBody').innerHTML = `
                <div style="display: grid; gap: 1rem;">
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <div class="profile-avatar" style="width: 80px; height: 80px; font-size: 2rem; margin: 0 auto 1rem;">
                            ${getInitials(user.firstName, user.lastName)}
                        </div>
                        <h3>${user.firstName} ${user.lastName}</h3>
                        <p style="color: var(--text-light);">${user.email}</p>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <strong>Role:</strong><br>
                            <span class="status-badge ${user.role === 'admin' ? 'status-pending' : 'status-active'}">${user.role}</span>
                        </div>
                        <div>
                            <strong>Status:</strong><br>
                            <span class="status-badge ${getStatusBadgeClass(user.status)}">${user.status}</span>
                        </div>
                        <div>
                            <strong>Phone:</strong><br>
                            ${user.phone || 'Not provided'}
                        </div>
                        <div>
                            <strong>Join Date:</strong><br>
                            ${formatDate(user.joinDate)}
                        </div>
                        <div>
                            <strong>Last Login:</strong><br>
                            ${user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}
                        </div>
                        <div>
                            <strong>Total Orders:</strong><br>
                            ${user.stats.totalOrders}
                        </div>
                    </div>
                    
                    <div>
                        <strong>Address:</strong><br>
                        ${user.address || 'Not provided'}
                    </div>
                </div>
            `;
            
            showModal();
        }

        function editUser(userId) {
            // For now, just show view - in a real app, this would open an edit form
            viewUser(userId);
        }

        function suspendUser(userId) {
            if (confirm('Are you sure you want to suspend this user?')) {
                authManager.suspendUser(userId);
                loadUsers();
                loadUserStats();
            }
        }

        function activateUser(userId) {
            if (confirm('Are you sure you want to activate this user?')) {
                authManager.activateUser(userId);
                loadUsers();
                loadUserStats();
            }
        }

        function showModal() {
            document.getElementById('userModal').style.display = 'flex';
            document.getElementById('overlay').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('userModal').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
        }

        function viewUserProfile(userId) {
            // Store the user ID and redirect directly to user profile page
            sessionStorage.setItem('viewUserId', userId);
            window.location.href = 'user-profile.html';
        }
    </script>
</body>
</html>
