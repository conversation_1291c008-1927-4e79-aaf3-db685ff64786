// Admin Dashboard Functionality

class AdminManager {
    constructor() {
        this.orders = this.loadOrders();
        this.products = this.loadProducts();
        this.analytics = this.loadAnalytics();
        this.init();
    }

    init() {
        // Initialize demo data if none exists
        if (this.orders.length === 0) {
            this.initializeDemoOrders();
        }
        if (this.products.length === 0) {
            this.initializeDemoProducts();
        }
        if (!this.analytics.lastUpdated) {
            this.initializeDemoAnalytics();
        }
    }

    // Orders Management
    loadOrders() {
        const orders = localStorage.getItem('vaith_orders');
        return orders ? JSON.parse(orders) : [];
    }

    saveOrders() {
        localStorage.setItem('vaith_orders', JSON.stringify(this.orders));
    }

    initializeDemoOrders() {
        const demoOrders = [
            {
                id: 'ORD-001',
                userId: 2,
                customerName: '<PERSON>',
                customerEmail: '<EMAIL>',
                status: 'completed',
                total: 299.99,
                items: [
                    { id: 1, name: 'Premium T-Shirt', price: 49.99, quantity: 2, image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=100&h=100&fit=crop' },
                    { id: 2, name: 'Designer Jeans', price: 199.99, quantity: 1, image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=100&h=100&fit=crop' }
                ],
                shippingAddress: '456 User Avenue, City, State 67890',
                orderDate: '2024-01-15T10:30:00Z',
                shippedDate: '2024-01-16T14:20:00Z',
                deliveredDate: '2024-01-18T16:45:00Z'
            },
            {
                id: 'ORD-002',
                userId: 3,
                customerName: 'Jane Smith',
                customerEmail: '<EMAIL>',
                status: 'shipped',
                total: 159.99,
                items: [
                    { id: 3, name: 'Summer Dress', price: 89.99, quantity: 1, image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=100&h=100&fit=crop' },
                    { id: 4, name: 'Casual Sneakers', price: 69.99, quantity: 1, image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=100&h=100&fit=crop' }
                ],
                shippingAddress: '789 Customer Lane, City, State 54321',
                orderDate: '2024-01-20T09:15:00Z',
                shippedDate: '2024-01-21T11:30:00Z',
                deliveredDate: null
            }
        ];

        this.orders = demoOrders;
        this.saveOrders();
    }

    // Products Management
    loadProducts() {
        const products = localStorage.getItem('vaith_products');
        return products ? JSON.parse(products) : [];
    }

    saveProducts() {
        localStorage.setItem('vaith_products', JSON.stringify(this.products));
    }

    initializeDemoProducts() {
        // Start with empty products array - no demo data
        this.products = [];
        this.saveProducts();
    }

    // Analytics Management
    loadAnalytics() {
        const analytics = localStorage.getItem('vaith_analytics');
        return analytics ? JSON.parse(analytics) : {};
    }

    saveAnalytics() {
        localStorage.setItem('vaith_analytics', JSON.stringify(this.analytics));
    }

    initializeDemoAnalytics() {
        const now = new Date();
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        
        this.analytics = {
            lastUpdated: now.toISOString(),
            revenue: {
                total: 12450.99,
                thisMonth: 3250.50,
                lastMonth: 2890.75,
                growth: 12.4
            },
            orders: {
                total: 156,
                thisMonth: 42,
                lastMonth: 38,
                growth: 10.5
            },
            customers: {
                total: 89,
                new: 12,
                returning: 77,
                growth: 15.2
            },
            products: {
                total: 89,
                active: 85,
                outOfStock: 4,
                lowStock: 8
            },
            topProducts: [
                { id: 1, name: 'Premium T-Shirt', sales: 45, revenue: 2249.55 },
                { id: 2, name: 'Designer Jeans', sales: 23, revenue: 4599.77 }
            ],
            salesByCategory: {
                men: 7200.50,
                women: 5250.49
            },
            monthlyRevenue: [
                { month: 'Jan', revenue: 2890.75 },
                { month: 'Feb', revenue: 3250.50 },
                { month: 'Mar', revenue: 2950.25 },
                { month: 'Apr', revenue: 3359.49 }
            ]
        };

        this.saveAnalytics();
    }

    // Order Methods
    getAllOrders() {
        return this.orders;
    }

    getOrderById(id) {
        return this.orders.find(order => order.id === id);
    }

    updateOrderStatus(orderId, status) {
        const orderIndex = this.orders.findIndex(order => order.id === orderId);
        if (orderIndex !== -1) {
            this.orders[orderIndex].status = status;
            
            // Update timestamps based on status
            const now = new Date().toISOString();
            if (status === 'shipped' && !this.orders[orderIndex].shippedDate) {
                this.orders[orderIndex].shippedDate = now;
            } else if (status === 'delivered' && !this.orders[orderIndex].deliveredDate) {
                this.orders[orderIndex].deliveredDate = now;
            }
            
            this.saveOrders();
            return this.orders[orderIndex];
        }
        return null;
    }

    searchOrders(query) {
        const searchTerm = query.toLowerCase();
        return this.orders.filter(order => 
            order.id.toLowerCase().includes(searchTerm) ||
            order.customerName.toLowerCase().includes(searchTerm) ||
            order.customerEmail.toLowerCase().includes(searchTerm)
        );
    }

    filterOrders(filters) {
        let filteredOrders = [...this.orders];

        if (filters.status) {
            filteredOrders = filteredOrders.filter(order => order.status === filters.status);
        }

        if (filters.dateFrom) {
            filteredOrders = filteredOrders.filter(order => 
                new Date(order.orderDate) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            filteredOrders = filteredOrders.filter(order => 
                new Date(order.orderDate) <= new Date(filters.dateTo)
            );
        }

        return filteredOrders;
    }

    // Product Methods
    getAllProducts() {
        return this.products;
    }

    getProductById(id) {
        return this.products.find(product => product.id === id);
    }

    createProduct(productData) {
        const newProduct = {
            id: Math.max(...this.products.map(p => p.id), 0) + 1,
            ...productData,
            createdDate: new Date().toISOString(),
            updatedDate: new Date().toISOString(),
            status: productData.status || 'active',
            rating: 0,
            reviews: 0
        };

        this.products.push(newProduct);
        this.saveProducts();
        return newProduct;
    }

    // Alias for createProduct
    addProduct(productData) {
        return this.createProduct(productData);
    }

    updateProduct(productId, updates) {
        const productIndex = this.products.findIndex(p => p.id === productId);
        if (productIndex !== -1) {
            this.products[productIndex] = {
                ...this.products[productIndex],
                ...updates,
                updatedDate: new Date().toISOString()
            };
            this.saveProducts();
            return this.products[productIndex];
        }
        return null;
    }

    deleteProduct(productId) {
        const productIndex = this.products.findIndex(p => p.id === productId);
        if (productIndex !== -1) {
            this.products.splice(productIndex, 1);
            this.saveProducts();
            return true;
        }
        return false;
    }

    searchProducts(query) {
        const searchTerm = query.toLowerCase();
        return this.products.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.brand.toLowerCase().includes(searchTerm) ||
            product.sku.toLowerCase().includes(searchTerm)
        );
    }

    filterProducts(filters) {
        let filteredProducts = [...this.products];

        if (filters.category) {
            filteredProducts = filteredProducts.filter(product => product.category === filters.category);
        }

        if (filters.status) {
            filteredProducts = filteredProducts.filter(product => product.status === filters.status);
        }

        if (filters.priceMin) {
            filteredProducts = filteredProducts.filter(product => product.price >= parseFloat(filters.priceMin));
        }

        if (filters.priceMax) {
            filteredProducts = filteredProducts.filter(product => product.price <= parseFloat(filters.priceMax));
        }

        return filteredProducts;
    }

    // Analytics Methods
    getAnalytics() {
        return this.analytics;
    }

    getDashboardStats() {
        const userStats = authManager.getUserStats();
        const orderStats = this.getOrderStats();
        const productStats = this.getProductStats();
        
        return {
            users: userStats,
            orders: orderStats,
            products: productStats,
            revenue: this.analytics.revenue
        };
    }

    getOrderStats() {
        const total = this.orders.length;
        const completed = this.orders.filter(o => o.status === 'completed').length;
        const pending = this.orders.filter(o => o.status === 'pending').length;
        const shipped = this.orders.filter(o => o.status === 'shipped').length;
        
        return {
            total,
            completed,
            pending,
            shipped,
            completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
        };
    }

    getProductStats() {
        const total = this.products.length;
        const active = this.products.filter(p => p.status === 'active').length;
        const outOfStock = this.products.filter(p => p.stock === 0).length;
        const lowStock = this.products.filter(p => p.stock > 0 && p.stock <= 5).length;
        
        return {
            total,
            active,
            outOfStock,
            lowStock,
            activePercentage: total > 0 ? Math.round((active / total) * 100) : 0
        };
    }
}

// Initialize global admin manager
window.adminManager = new AdminManager();

// Utility functions for admin UI
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatNumber(number) {
    return new Intl.NumberFormat('en-US').format(number);
}

function getOrderStatusBadgeClass(status) {
    const statusClasses = {
        'pending': 'status-pending',
        'processing': 'status-pending',
        'shipped': 'status-active',
        'delivered': 'status-completed',
        'completed': 'status-completed',
        'cancelled': 'status-cancelled',
        'refunded': 'status-suspended'
    };
    return statusClasses[status] || 'status-inactive';
}

function getProductStatusBadgeClass(status) {
    const statusClasses = {
        'active': 'status-active',
        'inactive': 'status-inactive',
        'draft': 'status-pending',
        'archived': 'status-suspended'
    };
    return statusClasses[status] || 'status-inactive';
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AdminManager, formatCurrency, formatNumber, getOrderStatusBadgeClass, getProductStatusBadgeClass };
}
